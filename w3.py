import sys
import pygetwindow as gw
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox,
    QLineEdit, QPushButton, QMessageBox, QGroupBox, QFormLayout, QSizePolicy,
    QDesktopWidget
)
from PyQt5.QtCore import Qt, QTimer

# 常见比例
COMMON_RATIOS = {
    "": None,  # 空白项
    "16:9": (16, 9),
    "4:3": (4, 3),
    "21:9": (21, 9),
    "1:1": (1, 1),
    "3:2": (3, 2),
    "自定义": None
}

# 常见分辨率（适配27寸和13寸屏）
COMMON_PIXELS = [
    "",  # 空白项
    "1920x1080",   # 16:9, 27寸常见
    "2560x1440",   # 16:9, 27寸高分
    "3840x2160",   # 16:9, 4K
    "2880x1800",   # 16:10, 13寸高分
    "1920x1200",   # 16:10, 13寸常见
    "2736x1824",   # 3:2, Surface Book等
    "2256x1504",   # 3:2, Surface Laptop
    "1600x900",    # 16:9, 13寸低分
    "1366x768"     # 16:9, 13寸低分
]

def get_window_list():
    windows = gw.getAllTitles()
    return [w for w in windows if w.strip()]

def parse_pixel_string(pixel_str):
    try:
        w, h = pixel_str.lower().split('x')
        return int(w), int(h)
    except Exception:
        return None, None

def get_all_monitors():
    """获取所有显示器信息"""
    desktop = QDesktopWidget()
    monitors = []

    for i in range(desktop.screenCount()):
        screen_rect = desktop.screenGeometry(i)
        available_rect = desktop.availableGeometry(i)
        is_primary = (i == desktop.primaryScreen())

        monitor_info = {
            'index': i,
            'geometry': {
                'x': screen_rect.x(),
                'y': screen_rect.y(),
                'width': screen_rect.width(),
                'height': screen_rect.height()
            },
            'available_geometry': {
                'x': available_rect.x(),
                'y': available_rect.y(),
                'width': available_rect.width(),
                'height': available_rect.height()
            },
            'is_primary': is_primary,
            'name': f"显示器 {i + 1}" + (" (主)" if is_primary else "")
        }
        monitors.append(monitor_info)

    return monitors

def get_screen_info():
    """获取主屏幕信息（保持向后兼容）"""
    desktop = QDesktopWidget()
    screen_rect = desktop.screenGeometry()
    return screen_rect.width(), screen_rect.height()

def get_screen_center(width, height, monitor_index=None):
    """获取屏幕中心位置坐标"""
    desktop = QDesktopWidget()

    if monitor_index is not None and monitor_index < desktop.screenCount():
        # 指定显示器的中心
        screen_rect = desktop.screenGeometry(monitor_index)
    else:
        # 主显示器的中心（保持向后兼容）
        screen_rect = desktop.screenGeometry()

    x = screen_rect.x() + (screen_rect.width() - width) // 2
    y = screen_rect.y() + (screen_rect.height() - height) // 2
    return x, y

def check_size_validity(width, height):
    """检查窗口尺寸是否超出屏幕范围"""
    screen_width, screen_height = get_screen_info()

    issues = []
    if width > screen_width:
        issues.append(f"宽度 {width}px 超出屏幕宽度 {screen_width}px")
    if height > screen_height:
        issues.append(f"高度 {height}px 超出屏幕高度 {screen_height}px")

    if issues:
        return False, "尺寸警告: " + ", ".join(issues)
    return True, "尺寸合适"

def set_window_size(window_title, width, height, center=True, monitor_index=None):
    """设置窗口尺寸，默认居中显示"""
    try:
        win = gw.getWindowsWithTitle(window_title)[0]
        win.restore()
        win.resizeTo(width, height)

        if center:
            x, y = get_screen_center(width, height, monitor_index)
            win.moveTo(x, y)

        win.activate()
        monitor_text = f" 在显示器 {monitor_index + 1}" if monitor_index is not None else ""
        return True, f"窗口已调整为 {width}x{height} 并居中显示{monitor_text}"
    except Exception as e:
        return False, f"调整失败: {e}"

def move_window_to_monitor(window_title, monitor_index):
    """将窗口移动到指定显示器"""
    try:
        monitors = get_all_monitors()
        if monitor_index >= len(monitors):
            return False, f"显示器 {monitor_index + 1} 不存在"

        win = gw.getWindowsWithTitle(window_title)[0]
        win.restore()

        # 获取目标显示器信息
        target_monitor = monitors[monitor_index]
        target_geometry = target_monitor['available_geometry']

        # 获取当前窗口尺寸
        current_width = win.width
        current_height = win.height

        # 确保窗口尺寸不超过目标显示器
        max_width = target_geometry['width']
        max_height = target_geometry['height']

        new_width = min(current_width, max_width)
        new_height = min(current_height, max_height)

        # 计算在目标显示器中的居中位置
        x = target_geometry['x'] + (target_geometry['width'] - new_width) // 2
        y = target_geometry['y'] + (target_geometry['height'] - new_height) // 2

        # 调整窗口尺寸和位置
        if new_width != current_width or new_height != current_height:
            win.resizeTo(new_width, new_height)

        win.moveTo(x, y)
        win.activate()

        return True, f"窗口已移动到{target_monitor['name']}"
    except Exception as e:
        return False, f"移动失败: {e}"

def maximize_window(window_title):
    """最大化窗口"""
    try:
        win = gw.getWindowsWithTitle(window_title)[0]
        win.maximize()
        win.activate()
        return True, "窗口已最大化"
    except Exception as e:
        return False, f"最大化失败: {e}"

def minimize_window(window_title):
    """最小化窗口"""
    try:
        win = gw.getWindowsWithTitle(window_title)[0]
        win.minimize()
        return True, "窗口已最小化"
    except Exception as e:
        return False, f"最小化失败: {e}"

def close_window(window_title):
    """关闭窗口"""
    try:
        win = gw.getWindowsWithTitle(window_title)[0]
        win.close()
        return True, "窗口已关闭"
    except Exception as e:
        return False, f"关闭失败: {e}"

def get_window_display_title(window_title):
    """获取窗口显示标题，格式：程序名 - 窗口标题"""
    if not window_title:
        return ""

    # 常见程序名映射
    program_mapping = {
        "chrome": "Chrome浏览器",
        "firefox": "Firefox浏览器",
        "notepad": "记事本",
        "code": "VS Code",
        "pycharm": "PyCharm",
        "word": "Microsoft Word",
        "excel": "Microsoft Excel",
        "powerpoint": "PowerPoint",
        "explorer": "文件资源管理器",
        "calculator": "计算器",
        "cmd": "命令提示符",
        "powershell": "PowerShell"
    }

    # 尝试识别程序类型
    title_lower = window_title.lower()
    program_name = "应用程序"

    for key, value in program_mapping.items():
        if key in title_lower:
            program_name = value
            break

    return f"{program_name} - {window_title}"

def calculate_ratio_size(ratio_tuple, current_width, current_height, scale_factor):
    """根据比例和缩放因子计算新尺寸"""
    if not ratio_tuple:
        return None, None

    ratio_w, ratio_h = ratio_tuple

    # 基于当前尺寸计算基准高度
    if current_width and current_height:
        # 使用当前尺寸作为基准
        base_height = current_height
    else:
        # 使用默认基准高度
        base_height = 600

    # 应用缩放因子
    new_height = int(base_height * scale_factor)
    new_width = int(new_height * ratio_w / ratio_h)

    return new_width, new_height

def validate_size_bounds(width, height):
    """验证尺寸是否在合理范围内"""
    MIN_SIZE = 300
    screen_width, screen_height = get_screen_info()

    # 检查最小尺寸
    if width < MIN_SIZE or height < MIN_SIZE:
        return False, f"尺寸不能小于 {MIN_SIZE}x{MIN_SIZE}"

    # 检查最大尺寸（不超过屏幕）
    if width > screen_width or height > screen_height:
        return False, f"尺寸不能超过屏幕尺寸 {screen_width}x{screen_height}"

    return True, "尺寸合适"

class WindowResizer(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("窗口管理工具 - 多显示器窗口控制")
        self.setFixedSize(400, 550)  # 增加宽度和高度以容纳显示器按钮
        self.monitors = get_all_monitors()  # 获取显示器信息
        self.init_ui()

    def init_ui(self):
        main_layout = QVBoxLayout()
        main_layout.setSpacing(8)  # 减小间距
        main_layout.setContentsMargins(10, 10, 10, 10)  # 减小边距

        # 1. 窗口选择区域 - 紧凑布局
        win_group = QGroupBox("目标窗口")
        win_layout = QVBoxLayout()
        win_layout.setSpacing(5)

        # 窗口选择行
        select_layout = QHBoxLayout()
        self.window_combo = QComboBox()
        self.window_combo.setMaximumHeight(24)  # 减小高度
        # 先不刷新窗口列表，等status_label创建后再刷新
        refresh_btn = QPushButton("刷新")
        refresh_btn.setMaximumSize(50, 24)  # 小按钮
        refresh_btn.clicked.connect(self.refresh_window_list)

        select_layout.addWidget(self.window_combo, 1)  # 拉伸
        select_layout.addWidget(refresh_btn)

        # 窗口控制按钮行 - 小按钮
        control_layout = QHBoxLayout()
        control_layout.setSpacing(3)

        self.maximize_btn = QPushButton("最大化")
        self.minimize_btn = QPushButton("最小化")
        self.close_btn = QPushButton("关闭")

        # 设置小按钮样式
        for btn in [self.maximize_btn, self.minimize_btn, self.close_btn]:
            btn.setMaximumHeight(26)
            btn.setMinimumHeight(26)
            btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        self.maximize_btn.clicked.connect(self.on_maximize_clicked)
        self.minimize_btn.clicked.connect(self.on_minimize_clicked)
        self.close_btn.clicked.connect(self.on_close_clicked)

        self.close_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; font-size: 11px; }")
        self.maximize_btn.setStyleSheet("QPushButton { font-size: 11px; }")
        self.minimize_btn.setStyleSheet("QPushButton { font-size: 11px; }")

        control_layout.addWidget(self.maximize_btn)
        control_layout.addWidget(self.minimize_btn)
        control_layout.addWidget(self.close_btn)

        win_layout.addLayout(select_layout)
        win_layout.addLayout(control_layout)
        win_group.setLayout(win_layout)
        main_layout.addWidget(win_group)

        # 2. 显示器选择区域
        monitor_group = QGroupBox("目标显示器")
        monitor_layout = QVBoxLayout()
        monitor_layout.setSpacing(5)

        # 显示器信息标签
        self.monitor_info_label = QLabel()
        self.monitor_info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 10px;
                color: #495057;
            }
        """)
        monitor_layout.addWidget(self.monitor_info_label)

        # 显示器按钮行
        monitor_btn_layout = QHBoxLayout()
        monitor_btn_layout.setSpacing(5)

        self.monitor_buttons = []
        for i in range(3):  # 最多支持3个显示器
            btn = QPushButton(f"显示器 {i + 1}")
            btn.setMinimumHeight(28)
            btn.setMaximumHeight(28)
            btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
            btn.clicked.connect(lambda checked=False, idx=i: self.on_monitor_button_clicked(idx))
            self.monitor_buttons.append(btn)
            monitor_btn_layout.addWidget(btn)

        monitor_layout.addLayout(monitor_btn_layout)
        monitor_group.setLayout(monitor_layout)
        main_layout.addWidget(monitor_group)

        # 更新显示器按钮状态
        self.update_monitor_buttons()

        # 3. 尺寸设置区域 - 紧凑布局
        size_group = QGroupBox("尺寸设置")
        size_layout = QVBoxLayout()
        size_layout.setSpacing(5)

        # 预设分辨率行
        pixel_layout = QHBoxLayout()
        pixel_layout.setSpacing(5)
        self.pixel_combo = QComboBox()
        self.pixel_combo.addItems(COMMON_PIXELS)
        self.pixel_combo.setCurrentIndex(0)
        self.pixel_combo.setMaximumHeight(24)
        self.pixel_combo.currentIndexChanged.connect(self.on_pixel_combo_changed)
        pixel_layout.addWidget(QLabel("预设:"))
        pixel_layout.addWidget(self.pixel_combo, 1)
        size_layout.addLayout(pixel_layout)

        # 自定义尺寸行 - 更紧凑
        custom_layout = QHBoxLayout()
        custom_layout.setSpacing(3)
        self.width_input = QLineEdit()
        self.width_input.setPlaceholderText("宽度")
        self.width_input.setMaximumHeight(24)
        self.width_input.setMaximumWidth(60)
        self.height_input = QLineEdit()
        self.height_input.setPlaceholderText("高度")
        self.height_input.setMaximumHeight(24)
        self.height_input.setMaximumWidth(60)

        custom_layout.addWidget(QLabel("自定义:"))
        custom_layout.addWidget(self.width_input)
        custom_layout.addWidget(QLabel("×"))
        custom_layout.addWidget(self.height_input)
        custom_layout.addStretch()  # 右侧留白
        size_layout.addLayout(custom_layout)

        # 比例设置行
        ratio_layout = QHBoxLayout()
        ratio_layout.setSpacing(3)
        self.ratio_combo = QComboBox()
        for k in COMMON_RATIOS.keys():
            self.ratio_combo.addItem(k)
        self.ratio_combo.setCurrentIndex(0)
        self.ratio_combo.setMaximumHeight(24)
        self.ratio_combo.currentIndexChanged.connect(self.on_ratio_changed)

        # 增大减小按钮
        self.enlarge_btn = QPushButton("增大")
        self.enlarge_btn.setMaximumSize(35, 24)
        self.enlarge_btn.setStyleSheet("QPushButton { font-size: 10px; background-color: #3498db; color: white; }")
        self.enlarge_btn.clicked.connect(self.on_enlarge_clicked)

        self.shrink_btn = QPushButton("减小")
        self.shrink_btn.setMaximumSize(35, 24)
        self.shrink_btn.setStyleSheet("QPushButton { font-size: 10px; background-color: #e67e22; color: white; }")
        self.shrink_btn.clicked.connect(self.on_shrink_clicked)

        ratio_layout.addWidget(QLabel("比例:"))
        ratio_layout.addWidget(self.ratio_combo, 1)
        ratio_layout.addWidget(self.enlarge_btn)
        ratio_layout.addWidget(self.shrink_btn)
        size_layout.addLayout(ratio_layout)

        size_group.setLayout(size_layout)
        main_layout.addWidget(size_group)

        # 4. 操作按钮区 - 紧凑按钮
        action_layout = QHBoxLayout()
        action_layout.setSpacing(8)

        self.set_btn = QPushButton("调整尺寸")
        self.set_btn.setMinimumHeight(32)
        self.set_btn.setMaximumHeight(32)
        self.set_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.set_btn.setStyleSheet("QPushButton { background-color: #27ae60; color: white; font-weight: bold; font-size: 12px; }")
        self.set_btn.clicked.connect(self.on_set_clicked)

        self.reset_btn = QPushButton("重置")
        self.reset_btn.setMinimumHeight(32)
        self.reset_btn.setMaximumHeight(32)
        self.reset_btn.setMaximumWidth(60)
        self.reset_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.reset_btn.setStyleSheet("QPushButton { font-size: 12px; }")
        self.reset_btn.clicked.connect(self.on_reset_clicked)

        action_layout.addWidget(self.set_btn, 1)
        action_layout.addWidget(self.reset_btn)
        main_layout.addLayout(action_layout)

        # 5. 消息状态栏
        self.status_label = QLabel("就绪")
        self.status_label.setMinimumHeight(24)
        self.status_label.setMaximumHeight(24)
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
                color: #2c3e50;
            }
        """)
        main_layout.addWidget(self.status_label)

        # 6. 初始化控件状态
        self.on_reset_clicked()

        # 7. 现在可以安全地刷新窗口列表了
        self.refresh_window_list()

        self.setLayout(main_layout)

    def show_message(self, message, msg_type="info"):
        """在状态栏显示消息"""
        colors = {
            "info": "#3498db",      # 蓝色 - 信息
            "success": "#27ae60",   # 绿色 - 成功
            "warning": "#f39c12",   # 橙色 - 警告
            "error": "#e74c3c"      # 红色 - 错误
        }

        color = colors.get(msg_type, colors["info"])
        self.status_label.setStyleSheet(f"""
            QLabel {{
                background-color: {color};
                border: 1px solid {color};
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
                color: white;
                font-weight: bold;
            }}
        """)
        self.status_label.setText(message)

        # 3秒后恢复默认状态
        QTimer.singleShot(3000, self.reset_status)

    def reset_status(self):
        """重置状态栏为默认状态"""
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
                color: #2c3e50;
            }
        """)
        self.status_label.setText("就绪")

    def update_monitor_buttons(self):
        """更新显示器按钮状态"""
        self.monitors = get_all_monitors()  # 刷新显示器信息

        # 更新信息标签
        monitor_count = len(self.monitors)
        primary_index = next((i for i, m in enumerate(self.monitors) if m['is_primary']), 0)
        info_text = f"检测到 {monitor_count} 个显示器，主显示器：显示器 {primary_index + 1}"
        self.monitor_info_label.setText(info_text)

        # 更新按钮状态
        for i, btn in enumerate(self.monitor_buttons):
            if i < monitor_count:
                # 活动显示器
                monitor = self.monitors[i]
                btn.setText(monitor['name'])
                btn.setEnabled(True)

                # 设置按钮样式
                if monitor['is_primary']:
                    # 主显示器 - 蓝色
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #3498db;
                            color: white;
                            font-weight: bold;
                            font-size: 11px;
                            border: 2px solid #2980b9;
                            border-radius: 4px;
                        }
                        QPushButton:hover {
                            background-color: #2980b9;
                        }
                        QPushButton:pressed {
                            background-color: #21618c;
                        }
                    """)
                else:
                    # 扩展显示器 - 绿色
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #27ae60;
                            color: white;
                            font-weight: bold;
                            font-size: 11px;
                            border: 2px solid #229954;
                            border-radius: 4px;
                        }
                        QPushButton:hover {
                            background-color: #229954;
                        }
                        QPushButton:pressed {
                            background-color: #1e8449;
                        }
                    """)
            else:
                # 非活动显示器 - 置灰
                btn.setText(f"显示器 {i + 1}")
                btn.setEnabled(False)
                btn.setStyleSheet("""
                    QPushButton {
                        background-color: #bdc3c7;
                        color: #7f8c8d;
                        font-size: 11px;
                        border: 2px solid #95a5a6;
                        border-radius: 4px;
                    }
                """)

    def on_monitor_button_clicked(self, monitor_index):
        """显示器按钮点击处理"""
        window_title = self.get_selected_window_title()
        if not window_title:
            self.show_message("请先选择一个窗口", "warning")
            return

        if monitor_index >= len(self.monitors):
            self.show_message(f"显示器 {monitor_index + 1} 不可用", "error")
            return

        # 移动窗口到指定显示器
        ok, msg = move_window_to_monitor(window_title, monitor_index)
        if ok:
            self.show_message(msg, "success")
        else:
            self.show_message(msg, "error")

    def refresh_window_list(self):
        """刷新窗口列表"""
        self.window_combo.clear()
        windows = get_window_list()
        # 为每个窗口添加程序类型前缀
        display_windows = [get_window_display_title(w) for w in windows]
        self.window_combo.addItems(display_windows)
        # 存储原始窗口标题用于操作
        self.original_windows = windows

        # 同时更新显示器按钮状态
        self.update_monitor_buttons()

        monitor_count = len(self.monitors)
        self.show_message(f"已刷新，找到 {len(windows)} 个窗口，{monitor_count} 个显示器", "info")

    def on_pixel_combo_changed(self):
        pixel_str = self.pixel_combo.currentText()
        if pixel_str and 'x' in pixel_str:
            w, h = parse_pixel_string(pixel_str)
            if w and h:
                self.width_input.setText(str(w))
                self.height_input.setText(str(h))
                self.ratio_combo.setCurrentIndex(0)  # 清空比例选择
        else:
            self.width_input.clear()
            self.height_input.clear()

        # 更新按钮状态
        self.update_ratio_buttons_state()

    def on_ratio_changed(self):
        """比例选择变化时的处理"""
        # 更新按钮状态
        self.update_ratio_buttons_state()

        # 如果选择了比例，清空像素预设
        if self.ratio_combo.currentText() and self.ratio_combo.currentText() not in ["", "自定义"]:
            self.pixel_combo.setCurrentIndex(0)

    def update_ratio_buttons_state(self):
        """更新增大减小按钮的启用状态"""
        ratio_text = self.ratio_combo.currentText()
        ratio_selected = bool(ratio_text and ratio_text not in ["", "自定义"])

        self.enlarge_btn.setEnabled(ratio_selected)
        self.shrink_btn.setEnabled(ratio_selected)

        if not ratio_selected:
            # 按钮置灰样式
            disabled_style = "QPushButton { font-size: 10px; background-color: #bdc3c7; color: #7f8c8d; }"
            self.enlarge_btn.setStyleSheet(disabled_style)
            self.shrink_btn.setStyleSheet(disabled_style)
        else:
            # 恢复正常样式
            self.enlarge_btn.setStyleSheet("QPushButton { font-size: 10px; background-color: #3498db; color: white; }")
            self.shrink_btn.setStyleSheet("QPushButton { font-size: 10px; background-color: #e67e22; color: white; }")

    def get_current_size_from_inputs(self):
        """从输入框获取当前尺寸"""
        try:
            width = int(self.width_input.text()) if self.width_input.text() else None
            height = int(self.height_input.text()) if self.height_input.text() else None
            return width, height
        except ValueError:
            return None, None

    def on_enlarge_clicked(self):
        """增大按钮点击处理"""
        ratio_key = self.ratio_combo.currentText()
        if not ratio_key or ratio_key in ["", "自定义"]:
            self.show_message("请先选择一个比例", "warning")
            return

        ratio = COMMON_RATIOS.get(ratio_key)
        if not ratio:
            self.show_message("无效的比例设置", "error")
            return

        # 获取当前尺寸
        current_w, current_h = self.get_current_size_from_inputs()

        # 计算新尺寸（增大20%）
        new_w, new_h = calculate_ratio_size(ratio, current_w, current_h, 1.2)

        # 验证尺寸范围
        valid, msg = validate_size_bounds(new_w, new_h)
        if not valid:
            self.show_message(f"无法增大: {msg}", "warning")
            return

        # 更新输入框
        self.width_input.setText(str(new_w))
        self.height_input.setText(str(new_h))

        self.show_message(f"按比例增大至 {new_w}x{new_h}", "success")

    def on_shrink_clicked(self):
        """减小按钮点击处理"""
        ratio_key = self.ratio_combo.currentText()
        if not ratio_key or ratio_key in ["", "自定义"]:
            self.show_message("请先选择一个比例", "warning")
            return

        ratio = COMMON_RATIOS.get(ratio_key)
        if not ratio:
            self.show_message("无效的比例设置", "error")
            return

        # 获取当前尺寸
        current_w, current_h = self.get_current_size_from_inputs()

        # 计算新尺寸（减小20%）
        new_w, new_h = calculate_ratio_size(ratio, current_w, current_h, 0.8)

        # 验证尺寸范围
        valid, msg = validate_size_bounds(new_w, new_h)
        if not valid:
            self.show_message(f"无法减小: {msg}", "warning")
            return

        # 更新输入框
        self.width_input.setText(str(new_w))
        self.height_input.setText(str(new_h))

        self.show_message(f"按比例减小至 {new_w}x{new_h}", "success")

    def get_selected_window_title(self):
        """获取当前选中的原始窗口标题"""
        current_index = self.window_combo.currentIndex()
        if current_index >= 0 and hasattr(self, 'original_windows') and current_index < len(self.original_windows):
            return self.original_windows[current_index]
        return ""

    def on_maximize_clicked(self):
        """最大化窗口"""
        window_title = self.get_selected_window_title()
        if not window_title:
            self.show_message("请先选择一个窗口", "warning")
            return

        ok, msg = maximize_window(window_title)
        if ok:
            self.show_message(msg, "success")
        else:
            self.show_message(msg, "error")

    def on_minimize_clicked(self):
        """最小化窗口"""
        window_title = self.get_selected_window_title()
        if not window_title:
            self.show_message("请先选择一个窗口", "warning")
            return

        ok, msg = minimize_window(window_title)
        if ok:
            self.show_message(msg, "success")
        else:
            self.show_message(msg, "error")

    def on_close_clicked(self):
        """关闭窗口"""
        window_title = self.get_selected_window_title()
        if not window_title:
            self.show_message("请先选择一个窗口", "warning")
            return

        # 确认对话框 - 这种需要用户确认的仍使用弹窗
        reply = QMessageBox.question(self, "确认关闭",
                                   f"确定要关闭窗口 '{window_title}' 吗？",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)

        if reply == QMessageBox.Yes:
            ok, msg = close_window(window_title)
            if ok:
                self.show_message(msg, "success")
                # 刷新窗口列表
                self.refresh_window_list()
            else:
                self.show_message(msg, "error")

    def on_set_clicked(self):
        """调整窗口尺寸 - 按优先级：比例 > 像素调整"""
        window_title = self.get_selected_window_title()
        if not window_title:
            self.show_message("请先选择一个窗口", "warning")
            return

        w, h = None, None
        method_used = ""

        # 第一优先级：按比例调整
        ratio_key = self.ratio_combo.currentText()
        if ratio_key and ratio_key not in ["", "自定义"] and ratio_key in COMMON_RATIOS:
            ratio = COMMON_RATIOS[ratio_key]
            if ratio:
                try:
                    # 优先使用高度输入，如果没有则使用默认600
                    if self.height_input.text():
                        h = int(self.height_input.text())
                    elif self.width_input.text():
                        w_temp = int(self.width_input.text())
                        h = int(w_temp * ratio[1] / ratio[0])
                    else:
                        h = 600  # 默认高度

                    w = int(h * ratio[0] / ratio[1])
                    method_used = f"按比例 {ratio_key}"
                except Exception:
                    self.show_message("比例计算失败，请检查输入的尺寸", "error")
                    return

        # 第二优先级：按预设像素调整
        if w is None or h is None:
            pixel_str = self.pixel_combo.currentText()
            if pixel_str and 'x' in pixel_str:
                w, h = parse_pixel_string(pixel_str)
                if w and h:
                    method_used = f"预设分辨率 {pixel_str}"

        # 第三优先级：按自定义像素调整
        if w is None or h is None:
            try:
                if self.width_input.text() and self.height_input.text():
                    w = int(self.width_input.text())
                    h = int(self.height_input.text())
                    method_used = "自定义尺寸"
                else:
                    self.show_message("请选择预设分辨率、设置比例或输入自定义尺寸", "warning")
                    return
            except Exception:
                self.show_message("自定义尺寸输入无效，请输入数字", "error")
                return

        # 检查尺寸是否超出屏幕
        size_ok, size_msg = check_size_validity(w, h)
        if not size_ok:
            self.show_message(size_msg, "warning")
            # 仍然继续执行，但给出警告

        # 执行窗口尺寸调整
        ok, msg = set_window_size(window_title, w, h, center=True)
        if ok:
            final_msg = f"{method_used}: {msg}"
            if not size_ok:
                final_msg += " (注意：尺寸超出屏幕)"
            self.show_message(final_msg, "success")
        else:
            self.show_message(f"调整失败: {msg}", "error")

    def on_reset_clicked(self):
        self.pixel_combo.setCurrentIndex(0)
        self.width_input.clear()
        self.height_input.clear()
        self.ratio_combo.setCurrentIndex(0)
        # 更新按钮状态
        self.update_ratio_buttons_state()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    resizer = WindowResizer()
    resizer.show()
    sys.exit(app.exec_())
