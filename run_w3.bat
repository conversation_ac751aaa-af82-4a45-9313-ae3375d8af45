@echo off
REM ============================================================================
REM Window Management Tool Launcher
REM ============================================================================
REM 
REM Author: Liu Lifu
REM Version: 2025-01-11-15:30:00
REM 
REM Purpose of this BAT file:
REM   This batch file serves as a launcher for the Window Management Tool (w3.py).
REM   It automatically handles the Python execution path and provides a convenient
REM   way to start the GUI application without manually navigating to the directory
REM   or typing the full Python command.
REM 
REM Purpose of w3.py:
REM   w3.py is a comprehensive window management tool that allows users to:
REM   - Resize application windows to specific dimensions or aspect ratios
REM   - Control window states (maximize, minimize, close)
REM   - Apply common screen resolutions (1920x1080, 2560x1440, etc.)
REM   - Maintain aspect ratios while scaling windows up or down
REM   - Center windows automatically after resizing
REM   - Validate window sizes against screen boundaries
REM 
REM Basic Usage:
REM   1. Double-click this BAT file to launch the Window Management Tool
REM   2. Select a target window from the dropdown list
REM   3. Choose resize method:
REM      - Select aspect ratio (16:9, 4:3, etc.) for proportional sizing
REM      - Choose preset resolution for common screen sizes
REM      - Enter custom width and height values
REM   4. Use control buttons:
REM      - "Enlarge/Shrink" buttons work only when aspect ratio is selected
REM      - "Maximize/Minimize/Close" buttons for window state control
REM      - "Adjust Size" button applies the selected dimensions
REM   5. Monitor status messages in the built-in status bar
REM 
REM System Requirements:
REM   - Python 3.x installed and accessible via PATH
REM   - PyQt5 library installed (pip install PyQt5)
REM   - pygetwindow library installed (pip install pygetwindow)
REM   - Windows operating system
REM 
REM Troubleshooting:
REM   - If Python is not found, ensure Python is installed and added to PATH
REM   - If modules are missing, install them using pip commands above
REM   - If GUI doesn't appear, check if display drivers are properly installed
REM 
REM ============================================================================

REM Change to the directory where this BAT file is located
cd /d "%~dp0"

REM Display startup message
echo ============================================================================
echo Window Management Tool - Starting...
echo Author: Liu Lifu
echo Version: 2025-01-11-15:30:00
echo ============================================================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not found in PATH
    echo Please install Python 3.x and ensure it's added to your system PATH
    echo.
    pause
    exit /b 1
)

REM Display Python version
echo Python version:
python --version
echo.

REM Check if w3.py exists in current directory
if not exist "w3.py" (
    echo ERROR: w3.py not found in current directory
    echo Please ensure w3.py is located in the same folder as this BAT file
    echo Current directory: %CD%
    echo.
    pause
    exit /b 1
)

REM Launch the Window Management Tool
echo Launching Window Management Tool...
echo.
echo Instructions:
echo - Select a window from the dropdown list
echo - Choose resize method (ratio/preset/custom)
echo - Use Enlarge/Shrink buttons when ratio is selected
echo - Click "Adjust Size" to apply changes
echo - Monitor status messages at the bottom
echo.
echo Press Ctrl+C in this window or close the GUI to exit
echo ============================================================================
echo.

REM Execute w3.py with Python
python w3.py

REM Check exit code
if errorlevel 1 (
    echo.
    echo ============================================================================
    echo Window Management Tool exited with an error
    echo Please check the error messages above for troubleshooting
    echo ============================================================================
) else (
    echo.
    echo ============================================================================
    echo Window Management Tool closed successfully
    echo ============================================================================
)

REM Pause to allow user to read any error messages
echo.
pause
